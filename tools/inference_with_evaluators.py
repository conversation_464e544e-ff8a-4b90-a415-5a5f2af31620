#!/usr/bin/env python3
"""
使用配置文件中定义的评价器进行推理和评价的脚本
直接调用CocoMetricIoU85、InstanceAwarePixelMetric等评价器
"""

import os
import argparse
import sys
import numpy as np
import cv2
import logging
from pathlib import Path

# Ensure project root is on PYTHONPATH
ROOT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir))
if ROOT_DIR not in sys.path:
    sys.path.insert(0, ROOT_DIR)

import torch
from mmdet.apis import inference_detector, init_detector
from mmengine.config import Config
from pycocotools.coco import COCO

# Import custom evaluators directly
from tools.custom_metrics.coco_metric_iou85 import CocoMetricIoU85
from tools.custom_metrics.improved_pixel_metric import ImprovedPixelMetric
from pycocotools.coco import COCO
import tempfile
import json
from pycocotools.cocoeval import COCOeval
from pycocotools import mask as mask_util

def setup_logging(output_dir):
    """Setup logging to both console and file, 参考inference_with_metrics.py"""
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Setup logging configuration
    log_file = os.path.join(output_dir, '0_log.txt')

    # Create logger
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    # Clear existing handlers
    logger.handlers.clear()

    # Create formatter
    formatter = logging.Formatter('%(message)s')

    # Create file handler
    file_handler = logging.FileHandler(log_file, mode='w', encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)

    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)

    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

def log_print(*args, **kwargs):
    """Custom print function that logs to both console and file"""
    message = ' '.join(str(arg) for arg in args)
    logging.info(message)

class InferenceCocoEvaluator:
    """专门用于推理场景的COCO评价器，参考inference_with_metrics.py的正确实现"""

    def __init__(self, ann_file, metric_type='segm', iou_threshold=0.85, output_dir=None):
        self.coco_gt = COCO(ann_file)
        self.metric_type = metric_type
        self.iou_threshold = iou_threshold
        self.predictions = []
        self.per_image_results = []  # 存储每个图像的评价结果
        self.output_dir = output_dir or 'output/coco_metrics'

        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)

        log_print(f"  📊 COCO Evaluator initialized with IoU threshold: {iou_threshold}")
        log_print(f"  📁 Output directory: {self.output_dir}")

    def add_prediction(self, img_id, result, filename=None):
        """添加单张图像的预测结果并立即计算指标，参考inference_with_metrics.py"""
        filename_info = f" ({filename})" if filename else ""

        if not hasattr(result, 'pred_instances'):
            log_print(f"  ⚠️  No pred_instances found for image {img_id}{filename_info}")
            # 仍然需要计算指标（可能有ground truth）
            per_image_metrics = self.calculate_per_image_metrics([], img_id, filename)
            self.per_image_results.append(per_image_metrics)
            return

        pred_instances = result.pred_instances
        if len(pred_instances) == 0:
            log_print(f"  📊 No predictions for image {img_id}{filename_info}")
            # 仍然需要计算指标（可能有ground truth）
            per_image_metrics = self.calculate_per_image_metrics([], img_id, filename)
            self.per_image_results.append(per_image_metrics)
            return

        # 转换为COCO格式的预测
        predictions = []
        log_print(f"  📊 Converting {len(pred_instances)} predictions to COCO format for image {img_id}{filename_info}")

        for i in range(len(pred_instances)):
            mask = pred_instances.masks[i].cpu().numpy().astype(np.uint8)
            score = float(pred_instances.scores[i].cpu())
            label = int(pred_instances.labels[i].cpu())

            # 转换mask为RLE格式
            rle = mask_util.encode(np.asfortranarray(mask))
            rle['counts'] = rle['counts'].decode('utf-8')

            prediction = {
                'image_id': img_id,
                'category_id': label,
                'segmentation': rle,
                'score': score
            }
            predictions.append(prediction)
            self.predictions.append(prediction)

            # 计算面积用于调试
            area = mask_util.area(rle)
            log_print(f"    Pred {i}: label={label}, area={area:.1f}, score={score:.3f}")

        # 计算该图像的指标
        per_image_metrics = self.calculate_per_image_metrics(predictions, img_id, filename)
        self.per_image_results.append(per_image_metrics)

    def calculate_per_image_metrics(self, predictions, img_id, filename=None):
        """改进版：准确计算单张图像在IoU=85%时的精确率和召回率"""
        # 获取ground truth
        ann_ids = self.coco_gt.getAnnIds(imgIds=[img_id])
        gt_anns = self.coco_gt.loadAnns(ann_ids)

        filename_info = f" ({filename})" if filename else ""
        log_print(f"  📊 Image {img_id}{filename_info}: {len(predictions)} predictions, {len(gt_anns)} ground truth")

        # 特殊情况处理
        if len(predictions) == 0 and len(gt_anns) == 0:
            return {
                'precision': 1.0,
                'recall': 1.0,
                'tp': 0,
                'fp': 0,
                'fn': 0,
                'matched_pairs': [],
                'img_id': img_id,
                'filename': filename
            }
        elif len(predictions) == 0:
            return {
                'precision': 0.0,
                'recall': 0.0,
                'tp': 0,
                'fp': 0,
                'fn': len(gt_anns),
                'matched_pairs': [],
                'img_id': img_id,
                'filename': filename
            }
        elif len(gt_anns) == 0:
            return {
                'precision': 0.0,
                'recall': 0.0,
                'tp': 0,
                'fp': len(predictions),
                'fn': 0,
                'matched_pairs': [],
                'img_id': img_id,
                'filename': filename
            }

        # 准备masks
        pred_masks = []
        gt_masks = []

        # 提取预测masks
        for pred in predictions:
            if 'segmentation' in pred:
                # RLE格式
                if isinstance(pred['segmentation'], dict):
                    mask = mask_util.decode(pred['segmentation'])
                else:
                    # 多边形格式，需要转换
                    h, w = self.coco_gt.imgs[img_id]['height'], self.coco_gt.imgs[img_id]['width']
                    mask = self._poly_to_mask(pred['segmentation'], h, w)
                pred_masks.append({
                    'mask': mask,
                    'score': pred['score'],
                    'category_id': pred['category_id']
                })

        # 提取GT masks
        for gt in gt_anns:
            if 'segmentation' in gt:
                if isinstance(gt['segmentation'], dict):
                    mask = mask_util.decode(gt['segmentation'])
                else:
                    h, w = self.coco_gt.imgs[img_id]['height'], self.coco_gt.imgs[img_id]['width']
                    mask = self._poly_to_mask(gt['segmentation'], h, w)
                gt_masks.append({
                    'mask': mask,
                    'category_id': gt['category_id']
                })

        # 按分数排序预测
        pred_masks.sort(key=lambda x: x['score'], reverse=True)

        # 计算IoU矩阵
        iou_matrix = np.zeros((len(pred_masks), len(gt_masks)))
        for i, pred in enumerate(pred_masks):
            for j, gt in enumerate(gt_masks):
                # 只计算相同类别的IoU
                if pred['category_id'] == gt['category_id']:
                    iou = self._compute_mask_iou(pred['mask'], gt['mask'])
                    iou_matrix[i, j] = iou

        # 贪心匹配：按预测分数从高到低匹配
        tp = 0
        fp = 0
        matched_gt = set()
        matched_pairs = []

        for i, pred in enumerate(pred_masks):
            best_iou = 0
            best_j = -1

            # 找到IoU最高且超过阈值的未匹配GT
            for j in range(len(gt_masks)):
                if j not in matched_gt and iou_matrix[i, j] >= self.iou_threshold:
                    if iou_matrix[i, j] > best_iou:
                        best_iou = iou_matrix[i, j]
                        best_j = j

            if best_j >= 0:
                # 找到匹配
                tp += 1
                matched_gt.add(best_j)
                matched_pairs.append({
                    'pred_idx': i,
                    'gt_idx': best_j,
                    'iou': best_iou,
                    'category_id': pred['category_id']
                })
            else:
                # 没有匹配，是false positive
                fp += 1

        # 未匹配的GT是false negatives
        fn = len(gt_masks) - len(matched_gt)

        # 计算精确率和召回率
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0

        log_print(f"  📊 Image {img_id} metrics @ IoU={self.iou_threshold}: ")
        log_print(f"     TP={tp}, FP={fp}, FN={fn}")
        log_print(f"     Precision={precision:.4f}, Recall={recall:.4f}")

        return {
            'precision': precision,
            'recall': recall,
            'tp': tp,
            'fp': fp,
            'fn': fn,
            'matched_pairs': matched_pairs,
            'img_id': img_id,
            'filename': filename
        }

    def _compute_mask_iou(self, mask1, mask2):
        """计算两个mask的IoU"""
        intersection = np.logical_and(mask1, mask2).sum()
        union = np.logical_or(mask1, mask2).sum()
        if union == 0:
            return 0.0
        return intersection / union

    def _poly_to_mask(self, polygons, height, width):
        """将多边形转换为mask"""
        rles = mask_util.frPyObjects(polygons, height, width)
        rle = mask_util.merge(rles)
        return mask_util.decode(rle)

    def _instance_to_coco_format(self, img_id, mask, label, score):
        """将单个实例转换为COCO格式"""
        try:
            # 确保mask是uint8格式
            if mask.dtype != np.uint8:
                mask = mask.astype(np.uint8)

            # 转换mask为RLE格式
            rle = mask_util.encode(np.asfortranarray(mask))
            if isinstance(rle['counts'], bytes):
                rle['counts'] = rle['counts'].decode('utf-8')

            # 计算面积和边界框
            area = float(mask_util.area(rle))
            bbox = mask_util.toBbox(rle).tolist()

            return {
                'image_id': int(img_id),
                'category_id': int(label),  # 保持原始类别ID
                'segmentation': rle,
                'score': float(score),
                'area': area,
                'bbox': bbox
            }
        except Exception as e:
            print(f"Warning: Failed to convert instance to COCO format: {e}")
            return None

    def evaluate(self):
        """改进的评价方法，提供更准确的整体指标"""
        if not self.per_image_results:
            log_print("Warning: No per-image results to evaluate")
            return {}

        # 计算整体的TP, FP, FN
        total_tp = sum(result['tp'] for result in self.per_image_results)
        total_fp = sum(result['fp'] for result in self.per_image_results)
        total_fn = sum(result['fn'] for result in self.per_image_results)

        # 计算整体精确率和召回率（微平均）
        overall_precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0.0
        overall_recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0.0
        overall_f1 = 2 * overall_precision * overall_recall / (overall_precision + overall_recall) \
                     if (overall_precision + overall_recall) > 0 else 0.0

        # 计算每张图像的平均指标（宏平均）
        precisions = [result['precision'] for result in self.per_image_results]
        recalls = [result['recall'] for result in self.per_image_results]
        f1_scores = [2 * p * r / (p + r) if (p + r) > 0 else 0.0
                     for p, r in zip(precisions, recalls)]

        macro_avg_precision = np.mean(precisions) if precisions else 0.0
        macro_avg_recall = np.mean(recalls) if recalls else 0.0
        macro_avg_f1 = np.mean(f1_scores) if f1_scores else 0.0

        # 计算每个类别的指标
        class_metrics = self._calculate_per_class_metrics()

        metrics = {
            # 微平均指标（基于总体TP/FP/FN）
            f'precision_micro@{int(self.iou_threshold*100)}': overall_precision,
            f'recall_micro@{int(self.iou_threshold*100)}': overall_recall,
            f'f1_micro@{int(self.iou_threshold*100)}': overall_f1,

            # 宏平均指标（每张图像的平均）
            f'precision_macro@{int(self.iou_threshold*100)}': macro_avg_precision,
            f'recall_macro@{int(self.iou_threshold*100)}': macro_avg_recall,
            f'f1_macro@{int(self.iou_threshold*100)}': macro_avg_f1,

            # 兼容性指标（保持原有命名）
            f'mAP_{int(self.iou_threshold*100)}': macro_avg_precision,
            'avg_precision': macro_avg_precision,
            'avg_recall': macro_avg_recall,
            'avg_f1_score': macro_avg_f1,
            'overall_precision': overall_precision,
            'overall_recall': overall_recall,
            'overall_f1_score': overall_f1,

            # 统计信息
            'total_tp': total_tp,
            'total_fp': total_fp,
            'total_fn': total_fn,
            'num_images': len(self.per_image_results),

            # 类别级指标
            'per_class_metrics': class_metrics
        }

        # 如果有预测结果，也计算标准COCO指标
        if self.predictions:
            try:
                coco_metrics = self._calculate_standard_coco_metrics()
                metrics.update(coco_metrics)
            except Exception as e:
                log_print(f"  ⚠️  Failed to calculate standard COCO metrics: {e}")

        # 保存详细结果
        self._save_detailed_results(metrics)

        log_print(f"\n📊 Evaluation Results @ IoU={self.iou_threshold}:")
        log_print(f"  Micro-averaged (Overall):")
        log_print(f"    Precision: {overall_precision:.4f}")
        log_print(f"    Recall: {overall_recall:.4f}")
        log_print(f"    F1-Score: {overall_f1:.4f}")
        log_print(f"  Macro-averaged (Per-image):")
        log_print(f"    Precision: {macro_avg_precision:.4f}")
        log_print(f"    Recall: {macro_avg_recall:.4f}")
        log_print(f"    F1-Score: {macro_avg_f1:.4f}")

        return metrics

    def _calculate_per_class_metrics(self):
        """计算每个类别的精确率和召回率"""
        class_stats = {}

        # 收集每个类别的TP, FP, FN
        for result in self.per_image_results:
            if 'matched_pairs' not in result:
                continue

            # 统计每个类别的匹配情况
            for pair in result.get('matched_pairs', []):
                cat_id = pair['category_id']
                if cat_id not in class_stats:
                    class_stats[cat_id] = {'tp': 0, 'fp': 0, 'fn': 0}
                class_stats[cat_id]['tp'] += 1

        # 从原始预测和GT中统计FP和FN
        for img_result in self.per_image_results:
            img_id = img_result.get('img_id')
            if not img_id:
                continue

            # 获取该图像的预测和GT
            img_predictions = [p for p in self.predictions if p['image_id'] == img_id]
            ann_ids = self.coco_gt.getAnnIds(imgIds=[img_id])
            gt_anns = self.coco_gt.loadAnns(ann_ids)

            # 统计每个类别的预测和GT数量
            pred_by_class = {}
            gt_by_class = {}

            for pred in img_predictions:
                cat_id = pred['category_id']
                pred_by_class[cat_id] = pred_by_class.get(cat_id, 0) + 1

            for gt in gt_anns:
                cat_id = gt['category_id']
                gt_by_class[cat_id] = gt_by_class.get(cat_id, 0) + 1

            # 计算每个类别的FP和FN
            all_cat_ids = set(pred_by_class.keys()) | set(gt_by_class.keys())
            for cat_id in all_cat_ids:
                if cat_id not in class_stats:
                    class_stats[cat_id] = {'tp': 0, 'fp': 0, 'fn': 0}

                pred_count = pred_by_class.get(cat_id, 0)
                gt_count = gt_by_class.get(cat_id, 0)
                tp_count = len([p for p in img_result.get('matched_pairs', [])
                              if p['category_id'] == cat_id])

                class_stats[cat_id]['fp'] += max(0, pred_count - tp_count)
                class_stats[cat_id]['fn'] += max(0, gt_count - tp_count)

        # 计算每个类别的指标
        class_metrics = {}
        for cat_id, stats in class_stats.items():
            tp = stats['tp']
            fp = stats['fp']
            fn = stats['fn']

            precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
            f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0

            # 获取类别名称
            cat_info = self.coco_gt.loadCats([cat_id])[0] if cat_id in self.coco_gt.getCatIds() else {'name': f'class_{cat_id}'}

            class_metrics[cat_id] = {
                'name': cat_info['name'],
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'tp': tp,
                'fp': fp,
                'fn': fn,
                'support': tp + fn  # GT数量
            }

        # 打印类别级结果
        log_print("\n📊 Per-Class Metrics @ IoU={}:".format(self.iou_threshold))
        log_print("  {:>20} {:>10} {:>10} {:>10} {:>8}".format(
            "Class", "Precision", "Recall", "F1-Score", "Support"))
        log_print("  " + "-" * 70)

        for cat_id in sorted(class_metrics.keys()):
            metrics = class_metrics[cat_id]
            log_print("  {:>20} {:>10.4f} {:>10.4f} {:>10.4f} {:>8d}".format(
                metrics['name'],
                metrics['precision'],
                metrics['recall'],
                metrics['f1_score'],
                metrics['support']
            ))

        return class_metrics

    def _save_detailed_results(self, results):
        """保存详细的评价结果到文件"""
        try:
            # 保存主要结果
            results_file = os.path.join(self.output_dir, 'coco_evaluation_results.json')
            with open(results_file, 'w') as f:
                json.dump(results, f, indent=2)
            log_print(f"  📁 COCO evaluation results saved to: {results_file}")

            # 保存每张图像的详细结果
            per_image_file = os.path.join(self.output_dir, 'per_image_results.json')
            with open(per_image_file, 'w') as f:
                json.dump(self.per_image_results, f, indent=2)
            log_print(f"  📁 Per-image results saved to: {per_image_file}")

        except Exception as e:
            log_print(f"  ⚠️  Failed to save detailed results: {e}")

    def _calculate_standard_coco_metrics(self):
        """计算标准COCO指标"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(self.predictions, f)
            temp_file = f.name

        try:
            coco_dt = self.coco_gt.loadRes(temp_file)
            coco_eval = COCOeval(self.coco_gt, coco_dt, self.metric_type)

            # 设置IoU阈值，确保包含0.85
            iou_thresholds = np.arange(0.5, 1.0, 0.05)
            if not any(abs(iou - 0.85) < 1e-5 for iou in iou_thresholds):
                iou_thresholds = np.append(iou_thresholds, 0.85)
                iou_thresholds = np.sort(iou_thresholds)
            coco_eval.params.iouThrs = iou_thresholds

            coco_eval.evaluate()
            coco_eval.accumulate()
            coco_eval.summarize()

            # 提取关键指标
            stats = coco_eval.stats
            return {
                'mAP': stats[0],
                'mAP_50': stats[1],
                'mAP_75': stats[2],
                'mAP_s': stats[3],
                'mAP_m': stats[4],
                'mAP_l': stats[5]
            }

        finally:
            if os.path.exists(temp_file):
                os.unlink(temp_file)

    def _extract_metrics(self, coco_eval):
        """提取评价指标"""
        metrics = {
            'mAP': float(coco_eval.stats[0]),
            'mAP_50': float(coco_eval.stats[1]),
            'mAP_75': float(coco_eval.stats[2]),
            'mAP_s': float(coco_eval.stats[3]),
            'mAP_m': float(coco_eval.stats[4]),
            'mAP_l': float(coco_eval.stats[5]),
        }

        # 计算mAP@85
        iou_idx = None
        for idx, iou in enumerate(coco_eval.params.iouThrs):
            if abs(iou - 0.85) < 1e-5:
                iou_idx = idx
                break

        if iou_idx is not None:
            # 获取precision数组: [TxRxKxAxM]
            precision = coco_eval.eval['precision']
            if precision.size > 0:
                # 选择IoU@0.85的precision值
                prec_85 = precision[iou_idx, :, :, 0, 2]  # [R, K] for area=all, maxDets=100
                valid_prec = prec_85[prec_85 > -1]
                if len(valid_prec) > 0:
                    metrics['mAP_85'] = float(np.mean(valid_prec))
                else:
                    metrics['mAP_85'] = 0.0
            else:
                metrics['mAP_85'] = 0.0
        else:
            metrics['mAP_85'] = 0.0

        return metrics

def validate_annotation_file(ann_file):
    """验证注释文件的结构和类别定义"""
    import json

    print("Validating annotation file...")
    with open(ann_file, 'r') as f:
        coco_data = json.load(f)

    categories = coco_data.get('categories', [])
    images = coco_data.get('images', [])
    annotations = coco_data.get('annotations', [])

    print(f"  Images: {len(images)}")
    print(f"  Annotations: {len(annotations)}")
    print(f"  Categories: {len(categories)}")

    # 检查类别ID的连续性
    cat_ids = sorted([cat['id'] for cat in categories])
    print(f"  Category IDs: {cat_ids}")

    if cat_ids != list(range(len(cat_ids))):
        print("  ⚠️  Warning: Category IDs are not continuous starting from 0")
        print("  This may cause label mapping issues")
    else:
        print("  ✓ Category IDs are continuous starting from 0")

    return categories

def parse_args():
    parser = argparse.ArgumentParser(description='Run inference with configured evaluators')
    parser.add_argument('--config', required=True, help='Config file path')
    parser.add_argument('--checkpoint', required=True, help='Checkpoint file path')
    parser.add_argument('--input', required=True, help='Input image file or directory')
    parser.add_argument('--output', required=True, help='Output directory')
    parser.add_argument('--ann-file', required=True, help='Annotation file for evaluation')
    parser.add_argument('--device', default='cuda:0', help='Device to use')
    parser.add_argument('--score-thr', type=float, default=0.3, help='Score threshold')
    parser.add_argument('--evaluators', nargs='+',
                       choices=['coco', 'pixel', 'improved_pixel'],
                       default=['coco', 'pixel'],
                       help='Which evaluators to run and print results for')
    parser.add_argument('--pixel-mode', default='instance_aware',
                       choices=['pure_pixel', 'instance_aware', 'weighted_pixel'],
                       help='Mode for ImprovedPixelMetric evaluator')
    parser.add_argument('--enable-house-type-eval', action='store_true',
                       help='Enable house type classification evaluation (rough house _uf_ vs finished house _ff_)')
    return parser.parse_args()

def get_image_files(input_path):
    """Get list of image files"""
    if os.path.isdir(input_path):
        image_files = []
        for ext in ['.png', '.jpg', '.jpeg']:
            image_files.extend(Path(input_path).glob(f'*{ext}'))
            image_files.extend(Path(input_path).glob(f'*{ext.upper()}'))
        # Filter out _gt files
        image_files = [f for f in image_files if '_gt' not in f.name.lower()]
        return sorted([str(f) for f in image_files])
    else:
        return [input_path]

def get_image_files(input_path):
    """Get list of image files"""
    if os.path.isdir(input_path):
        image_files = []
        for ext in ['.png', '.jpg', '.jpeg']:
            image_files.extend(Path(input_path).glob(f'*{ext}'))
            image_files.extend(Path(input_path).glob(f'*{ext.upper()}'))
        # Filter out _gt files
        image_files = [f for f in image_files if '_gt' not in f.name.lower()]
        return sorted([str(f) for f in image_files])
    else:
        return [input_path]



def get_img_id_by_filename(filename, coco_gt):
    """Get COCO image ID by filename"""
    base_name = os.path.splitext(os.path.basename(filename))[0]
    for img_id in coco_gt.getImgIds():
        img_info = coco_gt.loadImgs(img_id)[0]
        img_filename = os.path.splitext(img_info['file_name'])[0]
        if img_filename == base_name:
            return img_id
    return None

def classify_house_type(filename):
    """根据文件名分类房屋类型

    Args:
        filename (str): 图像文件名

    Returns:
        str: 'rough' (毛坯房_uf_), 'finished' (精装房_ff_), 'unknown' (未知类型)
    """
    filename_lower = filename.lower()
    if '_uf_' in filename_lower:
        return 'rough'  # 毛坯房
    elif '_ff_' in filename_lower:
        return 'finished'  # 精装房
    else:
        return 'unknown'  # 未知类型

def initialize_evaluators(args):
    """Initialize selected evaluators"""
    evaluators = {}

    # Load the actual categories from annotation file
    import json
    with open(args.ann_file, 'r') as f:
        coco_data = json.load(f)

    categories = coco_data.get('categories', [])
    class_names = [cat['name'] for cat in sorted(categories, key=lambda x: x['id'])]

    print(f"Found {len(class_names)} classes in annotation file:")
    for i, name in enumerate(class_names):
        print(f"  {i}: {name}")

    # Create dataset metadata with actual classes
    dataset_meta = {
        'classes': class_names,
        'palette': [[np.random.randint(0, 255) for _ in range(3)] for _ in class_names]
    }

    if 'coco' in args.evaluators:
        # 使用我们的专门推理评价器，它已经能正确计算mAP@85
        evaluator = InferenceCocoEvaluator(
            ann_file=args.ann_file,
            metric_type='segm'
        )
        evaluators['coco'] = evaluator
        print(f"✓ InferenceCocoEvaluator initialized with {len(class_names)} classes")

    # 'pixel' evaluator has been deprecated, use 'improved_pixel' instead

    if 'improved_pixel' in args.evaluators:
        # 启用二分类模式：房间类别 vs 背景类别
        evaluator = ImprovedPixelMetric(
            ann_file=args.ann_file,
            evaluation_mode=args.pixel_mode,
            num_classes=len(class_names),  # 使用正确的类别数
            class_names=class_names,  # 传递类别名称
            iou_threshold=0.5,
            score_threshold=args.score_thr,
            nms_pre=1000,
            output_dir=os.path.join(args.output, 'improved_pixel_metrics'),
            prefix='improved_pixel',
            # 启用二分类模式
            binary_classification=True,
            room_classes=list(range(15)),  # 前15个类别为房间类别
            non_room_classes=[15, 16, 17]  # 后3个类别为非房间类别（门窗等）
        )
        evaluator.dataset_meta = dataset_meta
        evaluators['improved_pixel'] = evaluator
        print(f"✓ ImprovedPixelMetric ({args.pixel_mode}) initialized in BINARY mode (Room vs Background)")

    return evaluators

def initialize_house_type_evaluators(args, class_names, dataset_meta):
    """为不同房屋类型初始化单独的评价器"""
    house_type_evaluators = {}

    # 房屋类型定义
    house_types = {
        'rough': '毛坯房(_uf_)',
        'finished': '精装房(_ff_)',
        'all': '所有房屋类型'
    }

    print(f"\n🏠 初始化房屋类型分类评价器:")

    for house_type, description in house_types.items():
        print(f"  📊 {description}")
        type_evaluators = {}

        # 为每种房屋类型创建COCO评价器（实例级指标）
        evaluator = InferenceCocoEvaluator(
            ann_file=args.ann_file,
            metric_type='segm',
            iou_threshold=0.85,
            output_dir=os.path.join(args.output, f'coco_metrics_{house_type}')
        )
        type_evaluators['coco'] = evaluator

        # 为每种房屋类型创建像素级评价器
        evaluator = ImprovedPixelMetric(
            ann_file=args.ann_file,
            evaluation_mode=args.pixel_mode,
            num_classes=len(class_names),
            class_names=class_names,
            iou_threshold=0.5,
            score_threshold=args.score_thr,
            nms_pre=1000,
            output_dir=os.path.join(args.output, f'improved_pixel_metrics_{house_type}'),
            prefix=f'improved_pixel_{house_type}',
            binary_classification=True,
            room_classes=list(range(13)),  # 根据用户修改的配置
            non_room_classes=[13, 14, 15]  # 根据用户修改的配置
        )
        evaluator.dataset_meta = dataset_meta
        type_evaluators['improved_pixel'] = evaluator

        house_type_evaluators[house_type] = type_evaluators

    return house_type_evaluators

def main():
    args = parse_args()

    # 设置日志系统
    setup_logging(args.output)

    # 验证注释文件
    log_print("="*80)
    log_print("ANNOTATION FILE VALIDATION")
    log_print("="*80)
    categories = validate_annotation_file(args.ann_file)

    log_print("\n" + "="*80)
    log_print("INFERENCE WITH SELECTED EVALUATORS")
    log_print("="*80)
    log_print(f"Config: {args.config}")
    log_print(f"Checkpoint: {args.checkpoint}")
    log_print(f"Input: {args.input}")
    log_print(f"Output: {args.output}")
    log_print(f"Annotation file: {args.ann_file}")
    log_print(f"Selected evaluators: {args.evaluators}")
    if 'improved_pixel' in args.evaluators:
        log_print(f"Pixel evaluation mode: {args.pixel_mode}")
    if args.enable_house_type_eval:
        log_print("House type classification evaluation: ENABLED")

    # Create output directory
    os.makedirs(args.output, exist_ok=True)

    # Load config and initialize model
    cfg = Config.fromfile(args.config)
    model = init_detector(cfg, args.checkpoint, device=args.device)
    print(f"✓ Model initialized on device: {args.device}")

    # Get image files
    image_files = get_image_files(args.input)
    print(f"✓ Found {len(image_files)} images to process")

    if len(image_files) == 0:
        print("❌ No image files found!")
        return

    # Initialize selected evaluators
    print("\n" + "-"*60)
    print("INITIALIZING EVALUATORS")
    print("-"*60)
    evaluators = initialize_evaluators(args)

    if not evaluators:
        print("❌ No evaluators initialized!")
        return

    # 获取类别信息用于房屋类型评价器
    import json
    with open(args.ann_file, 'r') as f:
        coco_data = json.load(f)
    categories = coco_data.get('categories', [])
    class_names = [cat['name'] for cat in sorted(categories, key=lambda x: x['id'])]
    dataset_meta = {
        'classes': class_names,
        'palette': [[np.random.randint(0, 255) for _ in range(3)] for _ in class_names]
    }

    # 初始化房屋类型分类评价器（如果启用）
    house_type_evaluators = {}
    if args.enable_house_type_eval:
        house_type_evaluators = initialize_house_type_evaluators(args, class_names, dataset_meta)

    # Load COCO annotations
    coco_gt = COCO(args.ann_file)
    print(f"✓ Loaded COCO annotations: {len(coco_gt.getImgIds())} images")

    # Process images
    log_print("\n" + "-"*60)
    log_print("PROCESSING IMAGES")
    log_print("-"*60)

    processed_count = 0

    for idx, img_path in enumerate(image_files, 1):
        filename = os.path.basename(img_path)
        log_print(f"\n[{idx}/{len(image_files)}] Processing: {filename}")
        log_print(f"  📁 Full path: {img_path}")

        # Get corresponding image ID from COCO
        img_id = get_img_id_by_filename(img_path, coco_gt)
        if img_id is None:
            log_print(f"  ⚠️  Could not find image ID for {filename}, skipping...")
            continue

        log_print(f"  📋 Image ID: {img_id}")

        # Run inference
        result = inference_detector(model, img_path)
        log_print(f"  🔍 Inference completed for {filename}")

        # Filter predictions by score threshold
        if hasattr(result, 'pred_instances'):
            valid_mask = result.pred_instances.scores > args.score_thr
            result.pred_instances = result.pred_instances[valid_mask]
            log_print(f"  📊 Found {len(result.pred_instances)} predictions above threshold {args.score_thr}")

            # Validate labels are within expected range
            if len(result.pred_instances) > 0:
                labels = result.pred_instances.labels.cpu().numpy()
                max_label = labels.max()
                min_label = labels.min()
                log_print(f"  🏷️  Predicted labels range: {min_label} to {max_label}")

                # Get the number of categories from COCO annotations
                num_categories = len(coco_gt.getCatIds())
                log_print(f"  📋 Available category IDs: {num_categories} categories (0 to {num_categories-1})")

                # Check for invalid labels
                if max_label >= num_categories:
                    log_print(f"  ⚠️  Warning: Max label {max_label} >= num_categories {num_categories}")
                    # Filter out invalid labels
                    valid_label_mask = result.pred_instances.labels < num_categories
                    if not valid_label_mask.all():
                        invalid_count = (~valid_label_mask).sum()
                        log_print(f"  🔧 Filtering out {invalid_count} predictions with invalid labels")
                        result.pred_instances = result.pred_instances[valid_label_mask]
                        log_print(f"  📊 After label filtering: {len(result.pred_instances)} predictions remain")
                else:
                    log_print(f"  ✓ All labels are within valid range")

        # 识别房屋类型（如果启用房屋类型评价）
        if args.enable_house_type_eval:
            house_type = classify_house_type(filename)
            house_type_name = {
                'rough': '毛坯房(_uf_)',
                'finished': '精装房(_ff_)',
                'unknown': '未知类型'
            }.get(house_type, '未知类型')
            log_print(f"  🏠 房屋类型: {house_type_name}")

        # Process with each evaluator
        for name, evaluator in evaluators.items():
            try:
                if name == 'coco':
                    # Use InferenceCocoEvaluator's add_prediction method
                    evaluator.add_prediction(img_id, result, filename)
                else:
                    # Custom evaluators expect dictionary format
                    dict_data_sample = {
                        'img_id': img_id,
                        'img_path': img_path,
                        'pred_instances': result.pred_instances
                    }
                    evaluator.process({}, [dict_data_sample])
            except Exception as e:
                log_print(f"  ❌ Error with {name} evaluator: {e}")

        # 处理房屋类型分类评价器（如果启用）
        if args.enable_house_type_eval and house_type_evaluators:
            # 1. 处理对应房屋类型的评价器
            if house_type in house_type_evaluators:
                for name, evaluator in house_type_evaluators[house_type].items():
                    try:
                        if name == 'coco':
                            evaluator.add_prediction(img_id, result, filename)
                        else:
                            dict_data_sample = {
                                'img_id': img_id,
                                'img_path': img_path,
                                'pred_instances': result.pred_instances
                            }
                            evaluator.process({}, [dict_data_sample])
                    except Exception as e:
                        log_print(f"  ❌ Error with {house_type} {name} evaluator: {e}")

            # 2. 处理"所有类型"评价器
            if 'all' in house_type_evaluators:
                for name, evaluator in house_type_evaluators['all'].items():
                    try:
                        if name == 'coco':
                            evaluator.add_prediction(img_id, result, filename)
                        else:
                            dict_data_sample = {
                                'img_id': img_id,
                                'img_path': img_path,
                                'pred_instances': result.pred_instances
                            }
                            evaluator.process({}, [dict_data_sample])
                    except Exception as e:
                        log_print(f"  ❌ Error with all-types {name} evaluator: {e}")

        processed_count += 1

        # Save simple visualization
        output_path = os.path.join(args.output, f"{os.path.splitext(os.path.basename(img_path))[0]}_result.png")
        img = cv2.imread(img_path)
        if hasattr(result, 'pred_instances') and len(result.pred_instances) > 0:
            masks = result.pred_instances.masks.cpu().numpy()
            for mask in masks:
                color = np.random.randint(0, 255, 3).tolist()
                colored_mask = np.zeros_like(img)
                colored_mask[mask > 0] = color
                img = cv2.addWeighted(img, 0.7, colored_mask, 0.3, 0)
        cv2.imwrite(output_path, img)
        print(f"  💾 Saved visualization: {os.path.basename(output_path)}")

    if processed_count == 0:
        print("❌ No images were successfully processed!")
        return

    print(f"\n✓ Successfully processed {processed_count} images")

    # Compute and display results for each evaluator
    print("\n" + "="*80)
    print("EVALUATION RESULTS")
    print("="*80)

    for name, evaluator in evaluators.items():
        print(f"\n{'='*20} {name.upper()} EVALUATOR {'='*20}")

        try:
            if name == 'coco':
                # Use InferenceCocoEvaluator's evaluate method
                metrics = evaluator.evaluate()
            else:
                # All other evaluators use standard compute_metrics method
                metrics = evaluator.compute_metrics(evaluator.results)

            if metrics:
                # Print metrics in a nice format
                for key, value in metrics.items():
                    if isinstance(value, (int, float)):
                        print(f"  {key}: {value:.4f}")
                    else:
                        print(f"  {key}: {value}")
            else:
                print("  ⚠️  No metrics computed")

        except Exception as e:
            print(f"  ❌ Error computing metrics: {e}")
            import traceback
            traceback.print_exc()

    # 计算并显示房屋类型分类的评价结果（如果启用）
    if args.enable_house_type_eval and house_type_evaluators:
        print("\n" + "="*80)
        print("HOUSE TYPE CLASSIFICATION EVALUATION RESULTS")
        print("="*80)

        house_type_names = {
            'rough': '毛坯房(_uf_)',
            'finished': '精装房(_ff_)',
            'all': '所有房屋类型'
        }

        for house_type, type_evaluators in house_type_evaluators.items():
            print(f"\n{'='*20} {house_type_names[house_type]} {'='*20}")

            for name, evaluator in type_evaluators.items():
                print(f"\n--- {name.upper()} EVALUATOR ---")

                try:
                    if name == 'coco':
                        metrics = evaluator.evaluate()
                    else:
                        metrics = evaluator.compute_metrics(evaluator.results)

                    if metrics:
                        for key, value in metrics.items():
                            if isinstance(value, (int, float)):
                                print(f"  {key}: {value:.4f}")
                            else:
                                print(f"  {key}: {value}")
                    else:
                        print("  ⚠️  No metrics computed")

                except Exception as e:
                    print(f"  ❌ Error computing {house_type} {name} metrics: {e}")

    print("\n" + "="*80)
    print("EVALUATION COMPLETED")
    print("="*80)
    print(f"📁 Results saved to: {args.output}")
    if args.enable_house_type_eval:
        print(f"📁 House type results saved to separate directories under: {args.output}")

if __name__ == '__main__':
    main()
