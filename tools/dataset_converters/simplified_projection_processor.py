#!/usr/bin/env python3
"""
简化的投影处理器 - 基于矢量图boundingBox的直接投影

根据上游同事的澄清：
- floorplan.json中的boundingBox是矢量图的边界，不是原始点云边界
- 矢量图在标注过程中已经去除了噪声点
- 我们直接使用矢量图的boundingBox作为坐标系基准
- 不需要复杂的坐标转换，直接使用网络训练时的坐标系
"""

import os
import sys
import argparse
import json
import numpy as np
import cv2
from pathlib import Path
from typing import Dict, Tuple, List, Optional
import tempfile
import shutil
from datetime import datetime
import glob

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入文件名转换器
try:
    from tools.dataset_converters.filename_converter import FilenameConverter
    FILENAME_CONVERTER_AVAILABLE = True
except ImportError as e:
    print(f"Warning: FilenameConverter import failed: {e}")
    FILENAME_CONVERTER_AVAILABLE = False

# 导入推理组件（可选）
try:
    from mmdet.apis import init_detector, inference_detector
    from mmdet.visualization import DetLocalVisualizer
    from tools.inference_with_metrics import InferenceMetricsCalculator
    from pycocotools import mask as maskUtils
    import torch
    INFERENCE_AVAILABLE = True
    print("✅ 推理组件加载成功")
except ImportError as e:
    print(f"❌ 推理组件导入失败: {e}")
    print("模型推理功能将不可用，仅支持预生成掩码处理")
    INFERENCE_AVAILABLE = False

class SimplifiedProjectionProcessor:
    """
    简化的投影处理器

    核心原理：
    1. 网络输出的256×256掩码保持原样，不进行任何坐标转换
    2. 直接生成256×256的灰度图像和RGB可视化图像
    3. mapping.txt保存calibration.json中的关键参数供下游使用
    4. 不关心像素分辨率或物理尺寸计算
    """

    def __init__(self, calibration_file: str, selected_filelist: Optional[str] = None,
                 model_config: Optional[str] = None, model_checkpoint: Optional[str] = None,
                 device: str = "cuda:0", score_threshold: float = 0.5,
                 data_root: Optional[str] = None):
        """
        初始化处理器

        参数:
            calibration_file: 校准文件路径
            selected_filelist: 选定文件列表路径（可选，用于批处理过滤）
            model_config: 模型配置文件路径（可选，用于直接推理）
            model_checkpoint: 模型检查点路径（可选，用于直接推理）
            device: 推理设备
            score_threshold: 预测分数阈值
            data_root: 数据根目录路径（用于读取floorplan.json）
        """
        self.calibration_file = calibration_file
        self.selected_files = set()
        self.data_root = data_root

        # 模型推理相关参数
        self.model_config = model_config
        self.model_checkpoint = model_checkpoint
        self.device = device
        self.score_threshold = score_threshold
        self.model = None
        self.visualizer = None

        # 加载校准数据
        self.calibration_data = self._load_calibration()

        # 初始化文件名转换器
        if FILENAME_CONVERTER_AVAILABLE:
            self.filename_converter = FilenameConverter()
        else:
            self.filename_converter = None
            print("⚠️  文件名转换器不可用，将使用基础文件名")

        # 加载选定文件列表（如果提供）
        if selected_filelist:
            self._load_selected_filelist(selected_filelist)

        # 如果提供了模型参数，初始化推理组件
        if model_config and model_checkpoint and INFERENCE_AVAILABLE:
            self._initialize_inference_components()

        print(f"🔧 简化投影处理器初始化完成")
        print(f"  校准文件: {calibration_file}")
        print(f"  加载场景数: {len(self.calibration_data['calib'])}")
        print(f"  输出格式: 256×256 灰度图像 + RGB可视化图像")
        if selected_filelist:
            print(f"  选定文件过滤: {len(self.selected_files)} 个文件")
        if self.model:
            print(f"  推理模式: 启用 (设备: {device}, 阈值: {score_threshold})")
    
    def _initialize_inference_components(self):
        """
        初始化模型推理组件

        加载模型、初始化可视化器，为直接推理做准备。
        """
        try:
            print(f"🔧 初始化推理组件...")
            print(f"  模型配置: {self.model_config}")
            print(f"  模型检查点: {self.model_checkpoint}")
            print(f"  设备: {self.device}")

            # 初始化模型
            self.model = init_detector(self.model_config, self.model_checkpoint, device=self.device)
            print(f"✅ 模型加载成功")

            # 初始化可视化器
            self.visualizer = DetLocalVisualizer()
            self.visualizer.dataset_meta = self.model.dataset_meta
            print(f"✅ 可视化器初始化成功")

            print(f"🎯 推理组件初始化完成，支持直接推理模式")

        except Exception as e:
            print(f"❌ 推理组件初始化失败: {e}")
            print("将回退到预生成掩码处理模式")
            self.model = None
            self.visualizer = None
            import traceback
            traceback.print_exc()

    def _load_calibration(self) -> Dict:
        """加载校准数据"""
        try:
            with open(self.calibration_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            raise RuntimeError(f"无法加载校准文件 {self.calibration_file}: {e}")

    def _load_pointcloud_roi_from_floorplan(self, scene_name: str) -> Optional[List[float]]:
        """
        从floorplan.json中读取pointcloud_roi

        参数:
            scene_name: 场景名称

        返回:
            pointcloud_roi列表 [min_x, min_y, min_z, max_x, max_y, max_z] 或 None
        """
        if not self.data_root:
            print(f"❌ 未提供data_root，无法读取floorplan.json")
            print(f"请在初始化SimplifiedProjectionProcessor时提供data_root参数")
            return None

        # 构建floorplan.json路径（复现generate_coco_hc_0722.py第205行）
        floorplan_path = os.path.join(self.data_root, scene_name, 'Annotations/floorplan.json')

        print(f"🔍 尝试读取floorplan.json: {floorplan_path}")

        if not os.path.exists(floorplan_path):
            print(f"❌ floorplan.json不存在: {floorplan_path}")
            print(f"请检查data_root路径和场景名称是否正确")
            return None

        try:
            # 读取floorplan.json（复现generate_coco_hc_0722.py第214-215行）
            with open(floorplan_path, 'r') as f:
                vectorized_gt = json.load(f)

            print(f"✅ 成功加载floorplan.json")

            # 检查结构
            if 'frames' not in vectorized_gt:
                print(f"❌ floorplan.json中缺少'frames'字段")
                return None

            if len(vectorized_gt['frames']) == 0:
                print(f"❌ floorplan.json中'frames'为空")
                return None

            if 'boundingBox' not in vectorized_gt['frames'][0]:
                print(f"❌ floorplan.json中frames[0]缺少'boundingBox'字段")
                return None

            # 提取pointcloud_roi（复现generate_coco_hc_0722.py第226行）
            pointcloud_roi = vectorized_gt['frames'][0]['boundingBox']

            # 验证格式
            if not isinstance(pointcloud_roi, list) or len(pointcloud_roi) != 6:
                print(f"❌ boundingBox格式错误，应该是6个元素的列表，实际: {pointcloud_roi}")
                return None

            print(f"✅ 成功从floorplan.json读取pointcloud_roi: {pointcloud_roi}")
            print(f"  X范围: {pointcloud_roi[0]:.3f} -> {pointcloud_roi[3]:.3f}")
            print(f"  Y范围: {pointcloud_roi[1]:.3f} -> {pointcloud_roi[4]:.3f}")
            print(f"  Z范围: {pointcloud_roi[2]:.3f} -> {pointcloud_roi[5]:.3f}")

            return pointcloud_roi

        except Exception as e:
            print(f"❌ 读取floorplan.json失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _load_selected_filelist(self, selected_filelist: str):
        """
        加载选定的文件列表用于过滤

        文件格式：目录\文件名 (例如: RS10_Batch_02\004_cuishanlantian_res-uf_RS10)

        参数:
            selected_filelist: 包含选定文件名的文件路径
        """
        try:
            with open(selected_filelist, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # 解析文件列表，格式为：目录\文件名
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#'):
                    # 分割目录和文件名
                    if '\\' in line:
                        batch_dir, filename = line.split('\\', 1)
                        # 存储完整的标识符用于匹配
                        self.selected_files.add(f"{batch_dir}\\{filename}")
                        # 同时存储单独的文件名用于兼容性
                        self.selected_files.add(filename)
                    else:
                        # 如果没有目录分隔符，直接添加
                        self.selected_files.add(line)

            print(f"已加载 {len(self.selected_files)} 个选定文件标识符")
            print(f"示例: {list(self.selected_files)[:3]}")

        except Exception as e:
            print(f"加载选定文件列表时出错: {e}")
            self.selected_files = set()

    def _is_file_selected(self, batch_directory: str, filename: str) -> bool:
        """
        检查文件是否在选定文件列表中

        参数:
            batch_directory: 批次目录名 (例如: RS10_Batch_02)
            filename: 文件名 (例如: 004_cuishanlantian_res-uf_RS10)

        返回:
            如果文件被选中返回True，否则返回False
        """
        if not self.selected_files:
            return True  # 如果没有过滤列表，处理所有文件

        # 剔除_gt文件
        if '_gt' in filename:
            return False

        # 构建完整的文件标识符，按照selected_filelist.txt的格式
        full_identifier = f"{batch_directory}\\{filename}"
        base_filename = os.path.splitext(filename)[0]

        # 多种匹配方式
        is_selected = (
            full_identifier in self.selected_files or
            filename in self.selected_files or
            base_filename in self.selected_files
        )

        return is_selected

    def _get_calibration_for_scene(self, scene_name: str) -> Dict:
        """获取指定场景的校准参数"""
        for calib in self.calibration_data['calib']:
            if calib['scene_name'] == scene_name:
                return calib
        raise ValueError(f"未找到场景 {scene_name} 的校准数据")
    
    def _process_mask_256(self, mask_256: np.ndarray) -> np.ndarray:
        """
        处理256×256掩码

        根据新的要求，不进行任何坐标转换，直接返回256×256的掩码
        """
        print(f"🎯 处理256×256掩码:")
        print(f"  输入尺寸: {mask_256.shape[1]}×{mask_256.shape[0]}")
        print(f"  输出尺寸: 256×256 (保持不变)")

        # 确保输入是256×256
        if mask_256.shape != (256, 256):
            print(f"  ⚠️  输入尺寸不是256×256，进行调整")
            mask_256 = cv2.resize(mask_256, (256, 256), interpolation=cv2.INTER_NEAREST)

        # 统计实例数量
        unique_instances = np.unique(mask_256)
        unique_instances = unique_instances[unique_instances > 0]  # 排除背景

        print(f"  实例数量: {len(unique_instances)}")
        print(f"  非零像素: {np.sum(mask_256 > 0)}")

        return mask_256
    
    def _create_rgb_visualization(self, mask: np.ndarray) -> np.ndarray:
        """创建RGB可视化图像"""
        unique_instances = np.unique(mask)
        unique_instances = unique_instances[unique_instances > 0]  # 排除背景
        
        if len(unique_instances) == 0:
            return np.full((mask.shape[0], mask.shape[1], 3), 255, dtype=np.uint8)  # 白色背景
        
        # 创建RGB图像
        rgb_image = np.full((mask.shape[0], mask.shape[1], 3), 255, dtype=np.uint8)  # 白色背景
        
        # 为每个实例分配颜色
        colors = [
            (255, 0, 0),    # 红色
            (0, 255, 0),    # 绿色
            (0, 0, 255),    # 蓝色
            (255, 255, 0),  # 黄色
            (255, 0, 255),  # 品红
            (0, 255, 255),  # 青色
            (128, 0, 128),  # 紫色
            (255, 165, 0),  # 橙色
        ]
        
        for i, instance_id in enumerate(unique_instances):
            color = colors[i % len(colors)]
            rgb_image[mask == instance_id] = color
        
        return rgb_image
    
    def _create_mapping_content(self, scene_name: str, calib_params: Dict) -> str:
        """
        创建mapping.txt文件内容

        为下游同事提供完整的坐标转换信息：
        第1行：场景名称
        第2行：旋转角度（弧度）
        第3行：训练ROI最小坐标（roi_min，2个值）
        第4行：训练ROI最大坐标（roi_max，2个值）
        第5行：像素分辨率（米/像素）
        第6行：ROI尺寸（宽度 高度，单位：米）
        第7行：图像尺寸（宽度 高度，像素）
        第8行：原始boundingBox（6个值：min_x min_y min_z max_x max_y max_z）
        """
        rotation_angle = calib_params['rotation_angle']
        roi_min = calib_params['roi_min']
        roi_max = calib_params['roi_max']

        # 计算额外的有用信息
        roi_width = roi_max[0] - roi_min[0]
        roi_height = roi_max[1] - roi_min[1]
        pixel_resolution = max(roi_width, roi_height) / 256.0  # 米/像素

        # 直接从floorplan.json读取pointcloud_roi（不使用校准数据中的值）
        pointcloud_roi = self._load_pointcloud_roi_from_floorplan(scene_name)

        if pointcloud_roi is None:
            raise RuntimeError(f"❌ 无法从floorplan.json读取pointcloud_roi，请检查data_root路径和场景名称: {scene_name}")

        print(f"✅ 从floorplan.json读取到pointcloud_roi: {pointcloud_roi}")

        # 格式化内容
        content = f"{scene_name}\n"
        content += f"{rotation_angle:.12f}\n"
        content += f"{roi_min[0]:.6f} {roi_min[1]:.6f}\n"
        content += f"{roi_max[0]:.6f} {roi_max[1]:.6f}\n"
        content += f"{pixel_resolution:.6f}\n"
        content += f"{roi_width:.6f} {roi_height:.6f}\n"
        content += f"256 256\n"  # 固定图像尺寸
        content += f"{pointcloud_roi[0]:.6f} {pointcloud_roi[1]:.6f} {pointcloud_roi[2]:.6f} {pointcloud_roi[3]:.6f} {pointcloud_roi[4]:.6f} {pointcloud_roi[5]:.6f}\n"

        print(f"📋 生成mapping.txt内容:")
        print(f"  场景名称: {scene_name}")
        print(f"  旋转角度: {rotation_angle:.6f} 弧度 ({np.degrees(rotation_angle):.2f}°)")
        print(f"  训练ROI最小坐标: [{roi_min[0]:.6f}, {roi_min[1]:.6f}]")
        print(f"  训练ROI最大坐标: [{roi_max[0]:.6f}, {roi_max[1]:.6f}]")
        print(f"  像素分辨率: {pixel_resolution:.4f} 米/像素 ({pixel_resolution*100:.2f} 厘米/像素)")
        print(f"  ROI尺寸: {roi_width:.2f}m × {roi_height:.2f}m")
        print(f"  图像尺寸: 256×256")
        print(f"  原始pointcloud_roi: [{pointcloud_roi[0]:.3f}, {pointcloud_roi[1]:.3f}, {pointcloud_roi[2]:.3f}] -> [{pointcloud_roi[3]:.3f}, {pointcloud_roi[4]:.3f}, {pointcloud_roi[5]:.3f}]")

        return content

    def _perform_inference_on_image(self, image_path: str) -> Optional[np.ndarray]:
        """
        对单张图像执行模型推理，生成实例分割掩码

        参数:
            image_path: 输入图像路径

        返回:
            实例分割掩码数组，每个实例有唯一ID，背景为0；失败返回None
        """
        if not self.model:
            print(f"❌ 模型未初始化，无法执行推理")
            return None

        try:
            # 加载图像
            image = cv2.imread(image_path)
            if image is None:
                print(f"❌ 无法加载图像: {image_path}")
                return None

            # BGR转RGB用于推理
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

            print(f"🔍 执行推理: {os.path.basename(image_path)}")

            # 执行推理
            result = inference_detector(self.model, image_rgb)

            # 提取预测结果
            if not hasattr(result, 'pred_instances'):
                print(f"❌ 推理结果格式异常")
                return None

            pred_instances = result.pred_instances

            # 根据分数阈值过滤
            valid_indices = pred_instances.scores > self.score_threshold
            if valid_indices.sum() == 0:
                print(f"⚠️  未找到满足分数阈值 {self.score_threshold} 的预测")
                return np.zeros(image_rgb.shape[:2], dtype=np.uint8)

            # 获取有效的掩码
            masks = pred_instances.masks[valid_indices]
            scores = pred_instances.scores[valid_indices]

            print(f"✅ 推理完成，找到 {len(masks)} 个有效实例")

            # 生成实例分割掩码
            h, w = image_rgb.shape[:2]
            instance_mask = np.zeros((h, w), dtype=np.uint8)

            # 为每个实例分配唯一ID（从1开始，0为背景）
            for i, mask in enumerate(masks):
                mask_array = mask.cpu().numpy().astype(np.uint8)
                instance_mask[mask_array > 0] = i + 1

            print(f"🎯 生成实例掩码: {instance_mask.shape}, 实例数: {len(masks)}")

            # 如果掩码不是256×256，调整尺寸
            if instance_mask.shape != (256, 256):
                instance_mask = cv2.resize(instance_mask, (256, 256), interpolation=cv2.INTER_NEAREST)
                print(f"📐 调整掩码尺寸到256×256")

            return instance_mask

        except Exception as e:
            print(f"❌ 推理过程出错: {e}")
            import traceback
            traceback.print_exc()
            return None

    def process_single_image(self, image_path: str, scene_name: str,
                           use_inference: bool = False) -> Dict:
        """
        处理单张图像

        参数:
            image_path: 输入图像路径
            scene_name: 场景名称
            use_inference: 是否使用模型推理

        返回:
            处理结果字典
        """
        print(f"\n🔍 处理图像: {image_path}")
        print(f"  场景名称: {scene_name}")
        print(f"  推理模式: {'启用' if use_inference else '禁用'}")

        # 获取校准参数
        try:
            calib_params = self._get_calibration_for_scene(scene_name)
        except ValueError as e:
            print(f"❌ {e}")
            return {'success': False, 'error': str(e)}

        # 获取掩码数据
        if use_inference and self.model:
            # 使用模型推理
            print(f"🤖 使用模型推理")
            mask_256 = self._perform_inference_on_image(image_path)
            if mask_256 is None:
                return {'success': False, 'error': '推理失败'}
        else:
            # 如果输入是掩码文件，直接加载
            if image_path.endswith('_mask.png'):
                print(f"📁 加载预生成掩码")
                mask_256 = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
                if mask_256 is None:
                    return {'success': False, 'error': '无法加载掩码文件'}
            else:
                # 创建模拟掩码用于测试
                print(f"⚠️  使用模拟预测结果")
                mask_256 = np.zeros((256, 256), dtype=np.uint8)
                # 添加一些模拟实例
                mask_256[50:100, 50:100] = 1
                mask_256[150:200, 150:200] = 2
                mask_256[100:150, 100:150] = 3

        # 处理256×256掩码（保持原样）
        final_mask = self._process_mask_256(mask_256)

        # 创建RGB可视化
        rgb_image = self._create_rgb_visualization(final_mask)

        # 创建mapping内容
        mapping_content = self._create_mapping_content(scene_name, calib_params)

        return {
            'success': True,
            'final_mask': final_mask,
            'rgb_image': rgb_image,
            'mapping_content': mapping_content,
            'output_size': (256, 256)  # 固定输出256×256
        }
    
    def save_results(self, result: Dict, output_dir: str, base_name: str):
        """
        保存处理结果
        
        参数:
            result: 处理结果
            output_dir: 输出目录
            base_name: 基础文件名
        """
        if not result['success']:
            print(f"❌ 无法保存失败的处理结果")
            return
        
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存灰度掩码
        # mask_path = os.path.join(output_dir, f"{base_name}_mask.png")
        mask_path = os.path.join(output_dir, f"DL_mask.png")
        cv2.imwrite(mask_path, result['final_mask'])
        
        # 保存RGB可视化
        # rgb_path = os.path.join(output_dir, f"{base_name}_rgb.png")
        rgb_path = os.path.join(output_dir, f"DL_rgb.png")
        cv2.imwrite(rgb_path, result['rgb_image'])
        
        # 保存mapping.txt
        # mapping_path = os.path.join(output_dir, f"{base_name}_mapping.txt")
        mapping_path = os.path.join(output_dir, f"DL_mapping.txt")
        with open(mapping_path, 'w') as f:
            f.write(result['mapping_content'])
        
        print(f"💾 结果已保存:")
        print(f"  灰度掩码: {mask_path}")
        print(f"  RGB可视化: {rgb_path}")
        print(f"  映射文件: {mapping_path}")
        print(f"  图像尺寸: {result['output_size'][0]}×{result['output_size'][1]}")

    def process_prediction_mask(self, mask_path: str, mask_filename: str,
                              output_base_dir: str) -> bool:
        """
        处理单个预测掩码文件

        完全按照原版reverse_projection_processor.py的逻辑

        参数:
            mask_path: 掩码文件路径
            mask_filename: 掩码文件名
            output_base_dir: 输出基础目录

        返回:
            成功返回True，失败返回False
        """
        try:
            base_name = os.path.splitext(mask_filename)[0]

            # 移除_mask后缀获取原始文件名
            if base_name.endswith('_mask'):
                original_name = base_name[:-5]  # 移除'_mask'
            else:
                original_name = base_name

            # 使用文件名转换器获取批次目录和新文件名
            if self.filename_converter:
                conversion_result = self.filename_converter.convert_single_filename(original_name)
                if not conversion_result:
                    print(f"文件名转换失败: {original_name}")
                    return False

                new_filename = conversion_result['new_filename']
                batch_directory = conversion_result['parent_directory']
            else:
                # 如果没有文件名转换器，使用基础文件名
                new_filename = original_name
                batch_directory = "default_batch"

            # 加载掩码图像
            mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
            if mask is None:
                print(f"加载掩码失败: {mask_path}")
                return False

            # 提取场景名称并获取校准参数
            # 使用原始文件名获取校准参数，而不是转换后的文件名
            if self.filename_converter:
                scene_name = self.filename_converter.extract_scene_name(original_name)
            else:
                scene_name = original_name

            try:
                calib_params = self._get_calibration_for_scene(scene_name)
            except ValueError as e:
                print(f"获取校准参数失败: {e}")
                print(f"  原始文件名: {original_name}")
                print(f"  提取的场景名: {scene_name}")
                return False

            # 创建输出目录
            output_dir = os.path.join(output_base_dir, batch_directory, new_filename)
            os.makedirs(output_dir, exist_ok=True)

            # 处理256×256掩码（保持原样）
            final_mask = self._process_mask_256(mask)

            # 创建RGB可视化
            rgb_image = self._create_rgb_visualization(final_mask)

            # 创建mapping内容
            mapping_content = self._create_mapping_content(scene_name, calib_params)

            # 保存结果
            result = {
                'success': True,
                'final_mask': final_mask,
                'rgb_image': rgb_image,
                'mapping_content': mapping_content,
                'output_size': (256, 256)
            }

            self.save_results(result, output_dir, new_filename)

            return True

        except Exception as e:
            print(f"处理 {mask_filename} 时出错: {e}")
            import traceback
            traceback.print_exc()
            return False

    def process_batch(self, input_dir: str, output_base_dir: str,
                     max_images: Optional[int] = None) -> Dict:
        """
        批处理掩码文件

        完全按照原版reverse_projection_processor.py的逻辑

        参数:
            input_dir: 输入目录路径
            output_base_dir: 输出基础目录
            max_images: 最大处理图像数（可选）

        返回:
            处理结果统计
        """
        input_path = Path(input_dir)
        all_mask_files = list(input_path.glob("*_mask.png"))

        if not all_mask_files:
            print(f"在 {input_dir} 中未找到 *_mask.png 文件")
            return {'total_files': 0, 'processed_successfully': 0, 'failed': 0, 'skipped': 0}

        # 先进行文件筛选
        print(f"🔍 开始文件筛选，总掩码文件数: {len(all_mask_files)}")
        selected_files = []

        for mask_file in all_mask_files:
            mask_filename = mask_file.name
            base_name = os.path.splitext(mask_filename)[0]

            # 移除_mask后缀获取原始文件名
            if base_name.endswith('_mask'):
                original_name = base_name[:-5]  # 移除'_mask'
            else:
                original_name = base_name

            # 剔除_gt文件
            if '_gt' in original_name:
                # print(f"跳过真值文件: {mask_filename}")
                continue

            # 如果有选定文件列表，进行筛选
            if self.selected_files:
                # 尝试从文件名转换获取批次目录
                if self.filename_converter:
                    conversion_result = self.filename_converter.convert_single_filename(original_name)
                    if conversion_result:
                        batch_directory = conversion_result['parent_directory']
                        new_filename = conversion_result['new_filename']

                        if self._is_file_selected(batch_directory, new_filename):
                            selected_files.append(mask_file)
                            print(f"✅ 选中掩码: {mask_filename} -> {batch_directory}\\{new_filename}")
                        else:
                            print(f"⏭️  跳过掩码: {mask_filename} (不在选定列表中)")
                    else:
                        print(f"⚠️  文件名转换失败: {original_name}")
                else:
                    # 没有文件名转换器，直接检查文件名
                    if self._is_file_selected("default_batch", original_name):
                        selected_files.append(mask_file)
                        print(f"✅ 选中掩码: {mask_filename}")
                    else:
                        print(f"⏭️  跳过掩码: {mask_filename} (不在选定列表中)")
            else:
                # 没有筛选列表，处理所有非_gt文件
                selected_files.append(mask_file)

        print(f"📋 筛选完成: {len(selected_files)}/{len(all_mask_files)} 个掩码文件将被处理")

        if not selected_files:
            print("⚠️  没有掩码文件需要处理")
            return {'total_files': len(all_mask_files), 'processed_successfully': 0, 'failed': 0, 'skipped': len(all_mask_files)}

        # 限制处理数量
        if max_images and len(selected_files) > max_images:
            selected_files = selected_files[:max_images]
            print(f"限制处理数量为 {max_images} 个文件")

        stats = {
            'total_files': len(all_mask_files),
            'processed_successfully': 0,
            'failed': 0,
            'skipped': len(all_mask_files) - len(selected_files)
        }

        print(f"🚀 开始批量掩码处理 {len(selected_files)} 个选中掩码...")

        for i, mask_file in enumerate(selected_files, 1):
            print(f"[{i}/{len(selected_files)}] 掩码处理: {mask_file.name}")

            try:
                success = self.process_prediction_mask(
                    str(mask_file), mask_file.name, output_base_dir
                )

                if success:
                    stats['processed_successfully'] += 1
                else:
                    stats['failed'] += 1

            except Exception as e:
                print(f"处理 {mask_file.name} 时出错: {e}")
                stats['failed'] += 1

        # 打印统计信息
        print(f"\n📊 批量掩码处理完成:")
        print(f"  总文件数: {stats['total_files']}")
        print(f"  筛选后处理: {len(selected_files)}")
        print(f"  成功处理: {stats['processed_successfully']}")
        print(f"  处理失败: {stats['failed']}")
        print(f"  跳过文件: {stats['skipped']}")

        return stats

    def process_image_with_inference(self, image_path: str, output_base_dir: str) -> bool:
        """
        对图像执行直接推理并进行处理

        完全按照原版reverse_projection_processor.py的逻辑

        参数:
            image_path: 输入图像路径
            output_base_dir: 输出基础目录

        返回:
            成功返回True，失败返回False
        """
        if not self.model:
            print(f"❌ 模型未初始化，请提供model_config和model_checkpoint参数")
            return False

        try:
            # 从图像路径提取文件名用于转换
            image_filename = os.path.basename(image_path)
            base_name = os.path.splitext(image_filename)[0]

            # 转换文件名格式
            if self.filename_converter:
                conversion_result = self.filename_converter.convert_single_filename(base_name)
                if not conversion_result:
                    print(f"文件名转换失败: {base_name}")
                    return False

                new_filename = conversion_result['new_filename']
                batch_directory = conversion_result['parent_directory']
            else:
                new_filename = base_name
                batch_directory = "default_batch"

            # 提取场景名称并获取校准参数
            # 使用原始文件名获取校准参数，而不是转换后的文件名
            if self.filename_converter:
                scene_name = self.filename_converter.extract_scene_name(base_name)
            else:
                scene_name = base_name

            try:
                calib_params = self._get_calibration_for_scene(scene_name)
            except ValueError as e:
                print(f"获取校准参数失败: {e}")
                print(f"  原始文件名: {base_name}")
                print(f"  提取的场景名: {scene_name}")
                return False

            # 执行推理生成实例掩码
            instance_mask = self._perform_inference_on_image(image_path)
            if instance_mask is None:
                return False

            # 创建输出目录
            output_dir = os.path.join(output_base_dir, batch_directory, new_filename)
            os.makedirs(output_dir, exist_ok=True)

            # 处理256×256掩码（保持原样）
            final_mask = self._process_mask_256(instance_mask)

            # 创建RGB可视化
            rgb_image = self._create_rgb_visualization(final_mask)

            # 创建mapping内容
            mapping_content = self._create_mapping_content(scene_name, calib_params)

            # 保存结果
            result = {
                'success': True,
                'final_mask': final_mask,
                'rgb_image': rgb_image,
                'mapping_content': mapping_content,
                'output_size': (256, 256)
            }

            self.save_results(result, output_dir, new_filename)

            return True

        except Exception as e:
            print(f"处理图像 {image_path} 时出错: {e}")
            import traceback
            traceback.print_exc()
            return False

    def process_batch_with_inference(self, input_dir: str, output_base_dir: str,
                                   file_pattern: str = "*.png",
                                   max_images: Optional[int] = None) -> Dict:
        """
        批量处理图像，先筛选再执行推理

        完全按照原版reverse_projection_processor.py的逻辑

        参数:
            input_dir: 包含输入图像的目录
            output_base_dir: 输出基础目录
            file_pattern: 文件匹配模式，默认"*.png"
            max_images: 最大处理图像数（可选）

        返回:
            包含处理统计信息的字典
        """
        if not self.model:
            print(f"❌ 模型未初始化，无法执行批量推理")
            return {'total_files': 0, 'processed_successfully': 0, 'failed': 0, 'skipped': 0}

        input_path = Path(input_dir)
        all_image_files = list(input_path.glob(file_pattern))

        if not all_image_files:
            print(f"在 {input_dir} 中未找到匹配模式 {file_pattern} 的文件")
            return {'total_files': 0, 'processed_successfully': 0, 'failed': 0, 'skipped': 0}

        # 先进行文件筛选
        print(f"🔍 开始文件筛选，总文件数: {len(all_image_files)}")
        selected_files = []

        for image_file in all_image_files:
            image_filename = image_file.name
            base_name = os.path.splitext(image_filename)[0]

            # 剔除_gt文件
            if '_gt' in base_name:
                # print(f"⏭️ 跳过真值文件: {image_filename}")
                continue

            # 如果有选定文件列表，进行筛选
            if self.selected_files:
                # 尝试从文件名转换获取批次目录
                if self.filename_converter:
                    conversion_result = self.filename_converter.convert_single_filename(base_name)
                    if conversion_result:
                        batch_directory = conversion_result['parent_directory']
                        new_filename = conversion_result['new_filename']

                        if self._is_file_selected(batch_directory, new_filename):
                            selected_files.append(image_file)
                            print(f"✅ 选中文件: {image_filename} -> {batch_directory}\\{new_filename}")
                        else:
                            print(f"⏭️ 跳过文件: {image_filename} (不在选定列表中)")
                    else:
                        print(f"⚠️  文件名转换失败: {base_name}")
                else:
                    # 没有文件名转换器，直接检查文件名
                    if self._is_file_selected("default_batch", base_name):
                        selected_files.append(image_file)
                        print(f"✅ 选中文件: {image_filename}")
                    else:
                        print(f"⏭️ 跳过文件: {image_filename} (不在选定列表中)")
            else:
                # 没有筛选列表，处理所有非_gt文件
                selected_files.append(image_file)

        print(f"📋 筛选完成: {len(selected_files)}/{len(all_image_files)} 个文件将被处理")

        if not selected_files:
            print("⚠️  没有文件需要处理")
            return {'total_files': len(all_image_files), 'processed_successfully': 0, 'failed': 0, 'skipped': len(all_image_files)}

        # 限制处理数量
        if max_images and len(selected_files) > max_images:
            selected_files = selected_files[:max_images]
            print(f"限制处理数量为 {max_images} 个文件")

        stats = {
            'total_files': len(all_image_files),
            'processed_successfully': 0,
            'failed': 0,
            'skipped': len(all_image_files) - len(selected_files)
        }

        print(f"🚀 开始批量推理处理 {len(selected_files)} 个选中图像...")

        for i, image_file in enumerate(selected_files, 1):
            print(f"[{i}/{len(selected_files)}] 推理处理: {image_file.name}")

            try:
                success = self.process_image_with_inference(
                    str(image_file), output_base_dir
                )

                if success:
                    stats['processed_successfully'] += 1
                else:
                    stats['failed'] += 1

            except Exception as e:
                print(f"处理 {image_file.name} 时出错: {e}")
                stats['failed'] += 1

        # 打印统计信息
        print(f"\n📊 批量推理处理完成:")
        print(f"  总文件数: {stats['total_files']}")
        print(f"  筛选后处理: {len(selected_files)}")
        print(f"  成功处理: {stats['processed_successfully']}")
        print(f"  处理失败: {stats['failed']}")
        print(f"  跳过文件: {stats['skipped']}")

        return stats


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="简化的投影处理器 - 批处理256×256图像")
    parser.add_argument("--calibration", required=True, help="校准文件路径")
    parser.add_argument("--input", required=True, help="输入目录路径（包含*_mask.png文件或图像文件）")
    parser.add_argument("--output-dir", required=True, help="输出基础目录")
    parser.add_argument("--data-root", default="/home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/",
                        help="数据根目录路径，用于读取floorplan.json（默认: /home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/）")
    parser.add_argument("--selected-filelist", help="选定文件列表路径（可选）")
    parser.add_argument("--max-images", type=int, help="最大处理图像数（可选）")
    parser.add_argument("--batch", action="store_true", help="批处理模式")

    # 推理相关参数
    parser.add_argument("--direct-inference", action="store_true", help="启用直接推理模式")
    parser.add_argument("--model-config", help="模型配置文件路径（推理模式必需）")
    parser.add_argument("--model-checkpoint", help="模型检查点路径（推理模式必需）")
    parser.add_argument("--device", default="cuda:0", help="推理设备（默认: cuda:0）")
    parser.add_argument("--score-threshold", type=float, default=0.5, help="预测分数阈值（默认: 0.5）")

    # 兼容单个文件处理的参数
    parser.add_argument("--scene-name", help="场景名称（单文件模式）")

    args = parser.parse_args()

    print("🚀 简化投影处理器 - 256×256输出")
    print("=" * 60)
    print(f"📁 数据根目录: {args.data_root}")
    print(f"📋 校准文件: {args.calibration}")
    print(f"📂 输入路径: {args.input}")
    print(f"📤 输出目录: {args.output_dir}")

    # 验证推理模式参数
    if args.direct_inference:
        if not INFERENCE_AVAILABLE:
            print("❌ 错误: 推理组件不可用，无法启用直接推理模式")
            print("请确保已正确安装mmdet、torch等依赖")
            return 1

        if not args.model_config or not args.model_checkpoint:
            print("❌ 错误: 直接推理模式需要提供 --model-config 和 --model-checkpoint 参数")
            return 1

        if not os.path.exists(args.model_config):
            print(f"❌ 错误: 模型配置文件不存在: {args.model_config}")
            return 1

        if not os.path.exists(args.model_checkpoint):
            print(f"❌ 错误: 模型检查点文件不存在: {args.model_checkpoint}")
            return 1

    # 初始化处理器
    processor = SimplifiedProjectionProcessor(
        calibration_file=args.calibration,
        selected_filelist=args.selected_filelist,
        model_config=args.model_config if args.direct_inference else None,
        model_checkpoint=args.model_checkpoint if args.direct_inference else None,
        device=args.device if args.direct_inference else "cuda:0",
        score_threshold=args.score_threshold if args.direct_inference else 0.5,
        data_root=args.data_root
    )

    if args.batch or os.path.isdir(args.input):
        # 批处理模式
        print("📁 批处理模式")

        if args.direct_inference:
            # 直接推理批处理
            print("🤖 使用直接推理模式")
            result = processor.process_batch_with_inference(
                input_dir=args.input,
                output_base_dir=args.output_dir,
                file_pattern="*.png",
                max_images=args.max_images
            )
        else:
            # 预生成掩码批处理
            print("📁 使用预生成掩码模式")
            result = processor.process_batch(
                input_dir=args.input,
                output_base_dir=args.output_dir,
                max_images=args.max_images
            )

        # 统一处理返回结果
        total_files = result.get('total_files', 0)
        processed_successfully = result.get('processed_successfully', 0)
        failed = result.get('failed', 0)
        skipped = result.get('skipped', 0)

        print(f"\n📊 批处理完成统计:")
        print(f"  总文件数: {total_files}")
        print(f"  成功处理: {processed_successfully}")
        print(f"  处理失败: {failed}")
        print(f"  跳过文件: {skipped}")

        if processed_successfully > 0:
            print(f"\n🎉 批处理完成！")
            print(f"✅ 成功处理 {processed_successfully}/{total_files} 个文件")
            print(f"✅ 输出256×256灰度掩码和RGB可视化图像")
            print(f"✅ mapping.txt包含calibration.json的关键参数")
            return 0
        else:
            print(f"\n❌ 没有文件被成功处理")
            return 1

    else:
        # 单文件处理模式
        print("📄 单文件处理模式")

        if args.direct_inference:
            # 直接推理单文件处理
            print("🤖 使用直接推理模式")
            success = processor.process_image_with_inference(
                image_path=args.input,
                output_base_dir=args.output_dir
            )

            if success:
                print(f"\n🎉 处理完成！")
                print(f"✅ 输出256×256灰度掩码和RGB可视化图像")
                print(f"✅ mapping.txt包含calibration.json的关键参数")
                return 0
            else:
                print(f"\n❌ 处理失败")
                return 1
        else:
            # 预生成掩码单文件处理
            print("📁 使用预生成掩码模式")

            if not args.scene_name:
                print("❌ 预生成掩码模式需要提供 --scene-name 参数")
                return 1

            result = processor.process_single_image(
                image_path=args.input,
                scene_name=args.scene_name,
                use_inference=False
            )

            # 保存结果
            base_name = Path(args.input).stem
            processor.save_results(result, args.output_dir, base_name)

            if result['success']:
                print(f"\n🎉 处理完成！")
                print(f"✅ 输出256×256灰度掩码和RGB可视化图像")
                print(f"✅ mapping.txt包含calibration.json的关键参数")
                return 0
            else:
                print(f"\n❌ 处理失败: {result.get('error', '未知错误')}")
                return 1


if __name__ == '__main__':
    exit(main())
